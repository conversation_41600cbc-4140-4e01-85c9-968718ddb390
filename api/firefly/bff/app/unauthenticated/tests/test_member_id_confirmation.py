import logging
import re
from datetime import datetime, timedelta
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.contrib.contenttypes.models import ContentType
from faker import Faker

from firefly.bff.app.unauthenticated.member_id_confirmation import RouteNames
from firefly.core.alias.models import AliasMapping, get_content_type
from firefly.core.tests.test_case import FireflyTestCase
from firefly.core.user.api.utils import get_current_bussiness_day
from firefly.core.user.constants import ASSIGNEE_GROUP_UNIQUE_KEY_MAP, DUPLICATE_ACCOUNT, PATIENT_GROUP_NAME
from firefly.core.user.factories import PersonUserFactory
from firefly.core.user.matching import match_and_merge_person
from firefly.core.user.models.models import Assignee<PERSON>roup, AssigneeGroupUser, Person, User
from firefly.modules.cases.models import Case, CaseCategory
from firefly.modules.eligibility.models import CoveredMember
from firefly.modules.facts.models import PreferredLanguage
from firefly.modules.firefly_django.constants import UTC_TIMEZONE
from firefly.modules.forms.constants import FormUID
from firefly.modules.forms.models import Form
from firefly.modules.insurance.constants import ContractAttributionType, ContractPMPMType, EmployerName
from firefly.modules.insurance.factories import ContractFactory
from firefly.modules.insurance.models import Employer, InsurancePayer, InsurancePlan
from firefly.modules.onboarding.statemachine.constants import OnboardingStatus
from firefly.modules.patient_referral_programs.models import PatientReferralProgramInfo
from firefly.modules.programs.benefit.constants import FIREFLY_PAYER, FIREFLY_PLAN
from firefly.modules.programs.constants import BenefitProgramStatus
from firefly.modules.programs.models import Program, ProgramEnrollment
from firefly.modules.programs.primary_care.utils import COVERAGE_INTAKE_SURVEY
from firefly.modules.programs.program_codes import ProgramCodes
from firefly.modules.programs.utils import add_person_to_program, person_is_in_program
from firefly.modules.statemachines.attribution.mixin import AttributionConstants
from firefly.modules.statemachines.coverage.constants import CoverageStates
from firefly.modules.tasks.constants import TaskCollectionTaskUniqueIdentifiers
from firefly.modules.tasks.models import SOURCE_TYPES, Task, TaskCollection, TaskCollectionTask
from firefly.modules.work_units.constants import StatusCategory

logger = logging.getLogger(__name__)
faker = Faker()


class MemberIdConfirmationTestCase(FireflyTestCase):
    def setUp(self):
        super().setUp()
        self.idexx_employer, _ = Employer.objects.get_or_create(name=EmployerName.IDEXX)
        self.firefly_employer, _ = Employer.objects.get_or_create(name=EmployerName.FIREFLY_HEALTH)
        ContractFactory(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": self.idexx_employer.id,
                "allowable_group_ids": ["123123"],
                "is_coverage_program_enrollment_enabled": True,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.AUTO_ATTRIBUTED,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.idexx_employer),
            contracted_entity=self.idexx_employer,
        )
        ContractFactory(
            config={
                "allowable_zip_codes": ["234324"],
                "employer_id": self.firefly_employer.id,
                "allowable_group_ids": ["123123"],
                "is_coverage_program_enrollment_enabled": True,
                "is_care_program_enrollment_enabled": True,
                "attribution_type": ContractAttributionType.OPT_IN_PCP,
                "plan_description_specific": False,
                "pmpm_category": ContractPMPMType.PMPM_MAYBE,
            },
            contracted_entity_content_type=get_content_type(self.firefly_employer),
            contracted_entity=self.firefly_employer,
        )
        self.firefly_payer, _ = InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        self.aetna_payer, _ = InsurancePayer.objects.get_or_create(name="Aetna")
        idexx_group_id, _ = AliasMapping.all_objects.get_or_create(
            object_id=self.idexx_employer.id,
            alias_name="Flume",
            content_type=ContentType.objects.get_for_model(Employer),
            alias_id="FF101",
        )
        firefly_group_id, _ = AliasMapping.all_objects.get_or_create(
            object_id=self.firefly_employer.id,
            alias_name="Flume",
            content_type=ContentType.objects.get_for_model(Employer),
            alias_id="FF100",
        )
        self.task_collection, _ = TaskCollection.objects.get_or_create(title=COVERAGE_INTAKE_SURVEY)
        form, _ = Form.objects.get_or_create(uid=FormUID.INTAKE_SURVEY_FORM)
        self.coverage_intake_template, _ = TaskCollectionTask.objects.update_or_create(
            uid=TaskCollectionTaskUniqueIdentifiers.COVERAGE_INTAKE_SURVEY,
            defaults={
                "task_collection": self.task_collection,
                "title": "Quick Coverage Check",
                "form": form,
                "source_type": SOURCE_TYPES["formsubmission"],
                "assign_to_patient": True,
            },
        )
        InsurancePlan.objects.get_or_create(name=FIREFLY_PLAN)
        self.preferred_language, _ = PreferredLanguage.objects.get_or_create(name="English")

    def _assert_task_assigment(self, person):
        task_uid_list = []
        if person.program_enrollments.filter(program__uid=ProgramCodes.PRIMARY_CARE).exists():
            task_uid_list.append(TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE)
        if (
            person.program_enrollments.filter(program__uid=ProgramCodes.PRIMARY_CARE).exists()
            and not person.program_enrollments.filter(program__uid=ProgramCodes.BENEFIT).exists()
        ):
            task_uid_list.append(TaskCollectionTaskUniqueIdentifiers.INSURANCE)
            task_uid_list.append(TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR)
        if person.program_enrollments.filter(program__uid=ProgramCodes.BENEFIT).exists():
            task_uid_list.append(TaskCollectionTaskUniqueIdentifiers.COVERAGE_INTAKE_SURVEY)
        for task_uid in task_uid_list:
            template = TaskCollectionTask.objects.get(uid=task_uid)
            task = Task.objects.filter(person=person, autocreated_from=template)
            self.assertTrue(task.exists())
            self.assertEqual(task.first().owner_group, person.user.assignee_group)

    def _assert_duplicate_account_case_creation(self, person):
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        care_coordinator_unique_key: str = ASSIGNEE_GROUP_UNIQUE_KEY_MAP["care_coordinator"]
        owner_group, created = AssigneeGroup.objects.get_or_create(unique_key=care_coordinator_unique_key)
        case = Case.objects.get(
            person=person,
            category=category,
        )
        self.assertIsNotNone(case)
        self.assertEqual(case.status_category, StatusCategory.NOT_STARTED)
        self.assertEqual(case.due_date.date(), get_current_bussiness_day())
        self.assertEqual(case.owner_group, owner_group)

    def _assert_duplicate_account_case_closed(self, person):
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.get(
            person=person,
            category=category,
        )
        self.assertIsNotNone(case)
        self.assertEqual(case.status_category, StatusCategory.COMPLETE)

    def _assert_merge_successfull(self, existing_person):
        existing_person.refresh_from_db()
        self.assertIsNotNone(existing_person.deleted)

    def _assert_person_creation(self, payload, user, existing_person=None):
        person = Person.objects.get(user_id=user.id)
        keys_to_exclude = [
            "preferred_language",
            "patient_referral_program",
            "insurance_member_info",
            "consent_forms",
        ]
        for key, value in payload.items():
            if key not in keys_to_exclude:
                self.assertEqual(getattr(person, key), value)
        if payload.get("preferred_language"):
            self.assertEqual(person.preferred_language.id, payload.get("preferred_language"))
        if existing_person:
            insurance_info = existing_person.insurance_info
            self.assertEqual(person.insurance_info.member_id, insurance_info.member_id)
            self.assertEqual(person.insurance_info.state, insurance_info.state)
            self.assertEqual(person.insurance_info.source_type, insurance_info.source_type)
            self.assertEqual(person.insurance_info.insurance_payer.id, insurance_info.insurance_payer.id)
        else:
            insurance_info = payload.get("insurance_member_info")
            self.assertEqual(person.insurance_info.member_id, insurance_info.get("member_id"))
            self.assertEqual(person.insurance_info.state, insurance_info.get("state"))
            self.assertEqual(person.insurance_info.source_type, insurance_info.get("source_type"))
            self.assertEqual(person.insurance_info.insurance_payer.id, insurance_info.get("insurance_payer_id"))
        if payload.get("patient_referral_program"):
            self.assertEqual(
                PatientReferralProgramInfo.objects.get(user=user).patient_referral_program.uid,
                payload.get("patient_referral_program"),
            )
        if payload.get("consent_forms"):
            self.assertTrue(user.user_consent_forms.filter(consent_form__id__in=payload.get("consent_forms")).exists())
        if person.insurance_info.insurance_payer.name == FIREFLY_PAYER:
            self.assertTrue(user.person.program_enrollments.filter(program__uid=ProgramCodes.BENEFIT).exists())
            self.assertEqual(user.onboarding_state.status, OnboardingStatus.MEMBER)
            self.assertIsNotNone(user.onboarding_state.member_at)
        else:
            self.assertTrue(user.person.program_enrollments.filter(program__uid=ProgramCodes.PRIMARY_CARE).exists())
            self.assertEqual(user.onboarding_state.status, OnboardingStatus.SIGNEDUP)
        self.assertIsNotNone(user.onboarding_state.signedup_at)
        self.assertIsNotNone(AssigneeGroup.objects.get(user=user))
        self.assertIsNotNone(AssigneeGroupUser.objects.get(user=user))
        user_groups = user.groups.values_list("name", flat=True)
        self.assertIn(PATIENT_GROUP_NAME, user_groups)
        self._assert_task_assigment(user.person)
        self.assertEqual(person.coverage, CoverageStates.PROCESSING)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_action_care_only_user(self, mocked_mfa):
        phone_number = "**********"
        mocked_mfa.return_value = phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        payload = {
            "first_name": "Test_care_Only",
            "last_name": "user",
            "phone_number": "",
            "preferred_name": "Care",
            "sex": "Male",
            "gender": ["Male"],
            "pronouns": "He/Him/His",
            "dob": "1992-11-22",
            "created_from": "app",
            "patient_referral_program": "",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12312312",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.InsurancePhotoUpload.value)
        payload["phone_number"] = phone_number
        payload["dob"] = datetime.strptime(payload["dob"], "%Y-%m-%d").date()
        self._assert_person_creation(
            payload=payload,
            user=user,
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.INIT)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_action_coverage_only_user_without_match(self, mocked_mfa):
        phone_number = "9782342123"
        mocked_mfa.return_value = phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        payload = {
            "first_name": "Star",
            "last_name": "Light",
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": "1992-11-21",
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.firefly_payer.id,
                "member_id": "MA12312312",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.AddressCollection.value)
        user.refresh_from_db()
        payload["phone_number"] = phone_number
        payload["dob"] = datetime.strptime(payload["dob"], "%Y-%m-%d").date()
        self._assert_person_creation(payload=payload, user=user)
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.INIT)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_exact_match(self, mocked_mfa):
        # User case 1:
        # Idexx Contact
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            member_id="FF00000002-00",
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
            termination_date=(datetime.now() + relativedelta(days=365)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        self.assertIsNone(existing_person.user)
        mocked_mfa.return_value = existing_person.phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.firefly_payer.id,
                "member_id": existing_person.insurance_info.member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.ConfirmAddress.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertEqual(existing_person.user, user)
        payload["phone_number"] = existing_person.phone_number
        self._assert_person_creation(payload=payload, user=user, existing_person=existing_person)
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.TERMINATED
            ).exists()
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.CONTRACTED)

        # Use Case 2
        # Firefly Contract
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Warner",
            dob="1945-03-17",
            phone_number="9123123122",
            employer_id=self.firefly_employer.id,
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Warner", phone_number="9123123122", dob="1945-03-17"
        )
        mocked_mfa.return_value = existing_person.phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.firefly_payer.id,
                "member_id": existing_person.insurance_info.member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.ConfirmAddress.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertEqual(existing_person.user, user)
        payload["phone_number"] = existing_person.phone_number
        self._assert_person_creation(payload=payload, user=user)
        self.assertFalse(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
            ).exists()
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.CONFIRMED)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_name_dob_member_id_match_and_phone_number_mismatch(self, mocked_mfa):
        # Since name, DOB, and member id matches even though ph_number mismatch, person will be merged on signed up and
        # coverage info will be overwritten
        member_id = "FF10119901-00"
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            member_id=member_id,
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        mocked_mfa.return_value = "**********"
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.ConfirmAddress.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertEqual(existing_person.user, user)
        payload["phone_number"] = mocked_mfa.return_value
        self._assert_person_creation(payload=payload, user=user, existing_person=existing_person)
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
            ).exists()
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.CONTRACTED)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_name_dob_phone_number_match_and_member_id_mismatch(self, mocked_mfa):
        # Since name, DOB, and Phone number matches, person will be merged on signed up and
        # coverage info will be overwritten
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        mocked_mfa.return_value = existing_person.phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12435",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.ConfirmAddress.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertEqual(existing_person.user, user)
        payload["phone_number"] = existing_person.phone_number
        self._assert_person_creation(payload=payload, user=user, existing_person=existing_person)
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
            ).exists()
        )
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.CONTRACTED)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_name_dob_match_and_phone_number_member_id_mismatch(self, mocked_mfa):
        # Since name, DOB, and Phone number matches, person will be merged on signed up and
        # coverage info will be overwritten
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        mocked_mfa.return_value = "**********"
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12435",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.InsurancePhotoUpload.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertIsNone(existing_person.user)
        payload["phone_number"] = mocked_mfa.return_value
        self._assert_duplicate_account_case_creation(sign_up_person)
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.INIT)

        # Merge using member ID the eligibility user and signed up user (Use case - When they upload correct insurance,
        # this will auto resolve)
        sign_up_person.insurance_info.member_id = existing_person.insurance_info.member_id
        sign_up_person.insurance_info.save()
        match_and_merge_person(
            dest_person_id=sign_up_person.id,
            member_id=sign_up_person.insurance_info.member_id,
            delete_src_person=True,
            dry_run=False,
        )
        user.refresh_from_db()
        self._assert_merge_successfull(existing_person=existing_person)
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ELECTED
            ).exists()
        )
        self.assertTrue(
            user.person.program_enrollments.filter(
                program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
            ).exists()
        )
        self._assert_duplicate_account_case_closed(sign_up_person)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_dob_match_and_name_phone_number_member_id_mismatch(self, mocked_mfa):
        # Since sonly DOB matches, person will be not be merged on signed up and case will not be created
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        mocked_mfa.return_value = "**********"
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": "Dav",
            "last_name": "Cronen",
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12435",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        self.assertIsNotNone(sign_up_person)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.InsurancePhotoUpload.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertIsNone(existing_person.user)
        self.assertNotEqual(existing_person, sign_up_person)
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.INIT)
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.filter(
            person=sign_up_person,
            category=category,
        )
        self.assertFalse(case.exists())

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_eligibility_ingestion_with_name_dob_ph_member_id_match(self, mocked_mfa):
        # ingested user should be merged with sign up user.
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        mocked_mfa.return_value = phone_number
        member_id = "FF10119901-00"

        payload = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "phone_number": phone_number,
            "preferred_name": faker.first_name(),
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": faker.date_of_birth(minimum_age=18, maximum_age=100).strftime("%Y-%m-%d"),
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        self._create_person_from_eligibility_file(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            member_id=member_id,
            dob=sign_up_person.dob.strftime("%Y-%m-%d"),
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            phone_number=phone_number,
            dob=sign_up_person.dob,
        )
        self.assertEqual(existing_person, sign_up_person)
        sign_up_person.refresh_from_db()
        self.assertEqual(sign_up_person.insurance_info.member_id, member_id)
        self.assertEqual(sign_up_person.employer, self.idexx_employer)
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.filter(
            person=sign_up_person,
            category=category,
        )
        self.assertFalse(case.exists())

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_eligibility_ingestion_with_name_dob_ph_match_and_member_id_mismatch(self, mocked_mfa):
        # ingested user should be merged with sign up user.
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        mocked_mfa.return_value = phone_number
        member_id = "FF10119901-00"

        payload = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "phone_number": phone_number,
            "preferred_name": faker.first_name(),
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": faker.date_of_birth(minimum_age=18, maximum_age=100).strftime("%Y-%m-%d"),
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12435",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        self._create_person_from_eligibility_file(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            member_id=member_id,
            dob=sign_up_person.dob.strftime("%Y-%m-%d"),
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            phone_number=phone_number,
            dob=sign_up_person.dob,
        )
        self.assertEqual(existing_person, sign_up_person)
        sign_up_person.refresh_from_db()
        # member id from file should be retained
        self.assertEqual(sign_up_person.insurance_info.member_id, member_id)
        sign_up_person.refresh_from_db()
        self.assertEqual(sign_up_person.employer, self.idexx_employer)
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.filter(
            person=sign_up_person,
            category=category,
        )
        self.assertFalse(case.exists())

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_eligibility_ingestion_with_name_dob_member_id_match_and_ph_mismatch(self, mocked_mfa):
        # ingested user should be merged with sign up user.
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        mocked_mfa.return_value = phone_number
        member_id = "FF10119901-00"

        payload = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "phone_number": phone_number,
            "preferred_name": faker.first_name(),
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": faker.date_of_birth(minimum_age=18, maximum_age=100).strftime("%Y-%m-%d"),
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        self._create_person_from_eligibility_file(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            member_id=member_id,
            dob=sign_up_person.dob.strftime("%Y-%m-%d"),
            phone_number="9847656756",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name=sign_up_person.first_name, last_name=sign_up_person.last_name, dob=sign_up_person.dob
        )
        self.assertEqual(existing_person, sign_up_person)
        sign_up_person.refresh_from_db()
        self.assertEqual(sign_up_person.insurance_info.member_id, member_id)
        # phone number from sign up should be retained
        self.assertEqual(sign_up_person.phone_number, phone_number)
        sign_up_person.refresh_from_db()
        self.assertEqual(sign_up_person.employer, self.idexx_employer)
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.filter(
            person=sign_up_person,
            category=category,
        )
        self.assertFalse(case.exists())

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_eligibility_ingestion_with_name_dob_match_and_ph_member_id_mismatch(self, mocked_mfa):
        # case should be created on person created from flume
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        mocked_mfa.return_value = phone_number
        member_id = "FF10119901-00"

        payload = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "phone_number": phone_number,
            "preferred_name": faker.first_name(),
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": faker.date_of_birth(minimum_age=18, maximum_age=100).strftime("%Y-%m-%d"),
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        self._create_person_from_eligibility_file(
            first_name=sign_up_person.first_name,
            last_name=sign_up_person.last_name,
            dob=sign_up_person.dob.strftime("%Y-%m-%d"),
            phone_number="9847656756",
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.filter(
            first_name=sign_up_person.first_name, last_name=sign_up_person.last_name, dob=sign_up_person.dob
        ).exclude(insurance_info__member_id=member_id)
        self.assertEqual(existing_person.count(), 1)
        existing_person = existing_person.first()
        self.assertNotEqual(existing_person, sign_up_person)
        self._assert_duplicate_account_case_creation(existing_person)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_eligibility_ingestion_with_dob_match_and_name_ph_member_id_mismatch(self, mocked_mfa):
        # person should not be linked and case should not be created
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        mocked_mfa.return_value = phone_number
        member_id = "FF10119901-00"

        payload = {
            "first_name": faker.first_name(),
            "last_name": faker.last_name(),
            "phone_number": phone_number,
            "preferred_name": faker.first_name(),
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": faker.date_of_birth(minimum_age=18, maximum_age=100).strftime("%Y-%m-%d"),
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)
        sign_up_person = Person.objects.get(user=user)
        first_name = faker.first_name()
        last_name = faker.last_name()
        phone_number = re.sub(r"\D", "", faker.phone_number())[:10]
        self._create_person_from_eligibility_file(
            first_name=first_name,
            last_name=last_name,
            dob=sign_up_person.dob.strftime("%Y-%m-%d"),
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name=first_name, last_name=last_name, dob=sign_up_person.dob, phone_number=phone_number
        )
        self.assertNotEqual(existing_person, sign_up_person)
        category = CaseCategory.objects.get(unique_key=DUPLICATE_ACCOUNT)
        case = Case.objects.filter(
            person=sign_up_person,
            category=category,
        )
        self.assertFalse(case.exists())

    def test_assign_segmentation_capture_on_enroll_for_benefit_user(self):
        InsurancePayer.objects.get_or_create(name=FIREFLY_PAYER)
        InsurancePlan.objects.get_or_create(name=FIREFLY_PLAN)
        Program.objects.get_or_create(uid=ProgramCodes.PRIMARY_CARE)
        Program.objects.get_or_create(uid=ProgramCodes.BENEFIT)

        person = PersonUserFactory.create()
        add_person_to_program(person=person, program_uid=ProgramCodes.BENEFIT)
        person.user.onboarding_state.signup_complete()
        person.refresh_from_db()
        self.assertEqual(person.user.onboarding_state.status, OnboardingStatus.MEMBER)
        add_person_to_program(person=person, program_uid=ProgramCodes.PRIMARY_CARE)
        segmentation_template = TaskCollectionTask.objects.get(
            uid=TaskCollectionTaskUniqueIdentifiers.SEGMENTATION_CAPTURE
        )
        task = Task.objects.filter(person=person, autocreated_from=segmentation_template)
        self.assertTrue(task.exists())
        cyd_template = TaskCollectionTask.objects.get(uid=TaskCollectionTaskUniqueIdentifiers.CHOOSE_YOUR_DOCTOR)
        task = Task.objects.filter(person=person, autocreated_from=cyd_template)
        self.assertTrue(task.exists())

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_signup_care_only_user_and_opt_for_coverage(self, mocked_mfa):
        """
        The insurane payer name should not get updated to firefly when the user
        has other active insurance and firefly insurance start date is in furture.
        """
        phone_number = "**********"
        mocked_mfa.return_value = phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        payload = {
            "first_name": "Home",
            "last_name": "Lander",
            "phone_number": "",
            "preferred_name": "Care",
            "sex": "Male",
            "gender": ["Male"],
            "pronouns": "He/Him/His",
            "dob": "1992-11-22",
            "created_from": "app",
            "patient_referral_program": "",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12312312",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertIsNotNone(user.person)
        user.person.insurance_info.insurance_payer = self.aetna_payer
        user.person.insurance_info.coverage_start = datetime.now(UTC_TIMEZONE) - timedelta(days=90)
        user.person.insurance_info.coverage_end = datetime.now(UTC_TIMEZONE) + timedelta(days=4)
        user.person.insurance_info.save()

        self._create_person_from_eligibility_file(
            first_name="Home",
            last_name="Lander",
            dob="1992-11-22",
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            member_id="FF00000002-00",
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        user.person.refresh_from_db()
        CoveredMember.objects.get(covered_member_person=user.person)
        self.assertTrue(person_is_in_program(person=user.person, program_uid=ProgramCodes.BENEFIT))
        self.assertEqual(user.person.insurance_info.insurance_payer, self.aetna_payer)

        user.person.insurance_info.insurance_payer = self.aetna_payer
        user.person.insurance_info.coverage_start = datetime.now(UTC_TIMEZONE) - timedelta(days=90)
        user.person.insurance_info.coverage_end = datetime.now(UTC_TIMEZONE) - timedelta(days=4)
        user.person.insurance_info.save()

        self._create_person_from_eligibility_file(
            first_name="Home",
            last_name="Lander",
            dob="1992-11-22",
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            member_id="FF00000002-00",
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        user.person.refresh_from_db()
        CoveredMember.objects.get(covered_member_person=user.person)
        self.assertTrue(person_is_in_program(person=user.person, program_uid=ProgramCodes.BENEFIT))
        self.assertEqual(user.person.insurance_info.insurance_payer, self.firefly_payer)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_signup_care_only_user_and_opt_for_coverage_with_coverage_started(self, mocked_mfa):
        """
        The insurane payer name should get updated to firefly when the firefly coverage starts
        """
        phone_number = "**********"
        mocked_mfa.return_value = phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)
        payload = {
            "first_name": "Home",
            "last_name": "Lander",
            "phone_number": "",
            "preferred_name": "Care",
            "sex": "Male",
            "gender": ["Male"],
            "pronouns": "He/Him/His",
            "dob": "1992-11-22",
            "created_from": "app",
            "patient_referral_program": "",
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.aetna_payer.id,
                "member_id": "MA12312312",
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertIsNotNone(user.person)
        user.person.insurance_info.insurance_payer = self.aetna_payer
        user.person.insurance_info.coverage_start = datetime.now(UTC_TIMEZONE) - timedelta(days=90)
        user.person.insurance_info.coverage_end = datetime.now(UTC_TIMEZONE) + timedelta(days=4)
        user.person.insurance_info.save()

        self._create_person_from_eligibility_file(
            first_name="Home",
            last_name="Lander",
            dob="1992-11-22",
            phone_number=phone_number,
            employer_id=self.idexx_employer.id,
            member_id="FF00000003-00",
            effective_start_date=(datetime.now() - relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        user.person.refresh_from_db()
        CoveredMember.objects.get(covered_member_person=user.person)
        self.assertTrue(person_is_in_program(person=user.person, program_uid=ProgramCodes.BENEFIT))
        self.assertEqual(user.person.insurance_info.insurance_payer, self.firefly_payer)

    @patch("firefly.bff.app.unauthenticated.member_id_confirmation.sync_phone_number")
    def test_coverage_only_users_with_exact_match_with_dual_enrolled_rows(self, mocked_mfa):
        """
        Vitori creates an person through eligibility file with start date as current year Jan 1st
        Victori updates the start date to next year Jan 1st
        User sign up Mid Year.
        Exceptation: User will not have elected row since the user is has enrolled row which overlaps with elected row.
        """
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            member_id="FF00000003-00",
            effective_start_date=(datetime.now() - relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        existing_person = Person.objects.get(
            first_name="David", last_name="Cronenberg", phone_number="**********", dob="1945-03-15"
        )
        program_enrollment = ProgramEnrollment.objects.filter(
            person=existing_person, program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(program_enrollment.count(), 1)
        self._create_person_from_eligibility_file(
            first_name="David",
            last_name="Cronenberg",
            dob="1945-03-15",
            phone_number="**********",
            employer_id=self.idexx_employer.id,
            member_id="FF00000003-00",
            effective_start_date=(datetime.now() + relativedelta(days=10)).date().strftime("%Y-%m-%d"),
        )
        program_enrollment = ProgramEnrollment.objects.filter(
            person=existing_person, program__uid=ProgramCodes.BENEFIT, status=BenefitProgramStatus.ENROLLED
        )
        self.assertEqual(program_enrollment.count(), 2)
        self.assertIsNone(existing_person.user)
        mocked_mfa.return_value = existing_person.phone_number
        user = User.objects.create(email="<EMAIL>")
        client = FireflyTestCase.get_member_client(member=user)

        payload = {
            "first_name": existing_person.first_name,
            "last_name": existing_person.last_name,
            "phone_number": "",
            "preferred_name": "Star",
            "sex": "Female",
            "gender": ["Female"],
            "pronouns": "She/her/hers",
            "dob": existing_person.dob,
            "created_from": "app",
            "preferred_language": self.preferred_language.id,
            "preferred_language_other": None,
            "insurance_member_info": {
                "state": "MA",
                "source_type": "employer",
                "insurance_payer_id": self.firefly_payer.id,
                "member_id": existing_person.insurance_info.member_id,
            },
            "consent_forms": [self.consent_form.id],
        }
        action_response = client.post("/bff/app/signup/member-id-confirmation/", payload, format="json")
        self.assertEqual(action_response.status_code, 200)

        # The FE receives a "route" object
        self.assertIn("route", action_response.data)
        self.assertEqual(action_response.data["route"], RouteNames.ConfirmAddress.value)
        existing_person.refresh_from_db()
        user.refresh_from_db()
        self.assertEqual(existing_person.user, user)
        payload["phone_number"] = existing_person.phone_number
        self._assert_person_creation(payload=payload, user=user, existing_person=existing_person)
        self.assertEqual(user.person.attribution.status, AttributionConstants.States.CONFIRMED)
